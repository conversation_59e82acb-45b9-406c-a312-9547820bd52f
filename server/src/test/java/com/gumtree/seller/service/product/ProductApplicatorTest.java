package com.gumtree.seller.service.product;

import com.google.common.collect.Sets;
import com.gumtree.common.util.time.Clock;
import com.gumtree.seller.category.TestCategoryModel;
import com.gumtree.seller.domain.advert.entity.Advert;
import com.gumtree.seller.domain.advert.entity.AdvertFeature;
import com.gumtree.seller.domain.advert.exception.AdvertAlreadyFeaturedException;
import com.gumtree.seller.domain.advert.exception.AdvertRecentlyPublishedException;
import com.gumtree.seller.domain.advert.exception.AdvertRemovedException;
import com.gumtree.seller.domain.advert.status.AdvertStatus;
import com.gumtree.seller.domain.image.entity.Image;
import com.gumtree.seller.domain.image.exception.ImageNotFoundException;
import com.gumtree.seller.domain.order.entity.OrderItem;
import com.gumtree.seller.domain.product.entity.Product;
import com.gumtree.seller.domain.product.entity.ProductName;
import com.gumtree.seller.event.listener.advert.AdvertListener;
import com.gumtree.seller.repository.advert.AdvertRepository;
import com.gumtree.seller.service.advert.FeatureApplicator;
import com.gumtree.seller.service.advert.expiry.CarsCategoryExpiryDateApplicator;
import com.gumtree.seller.service.advert.expiry.EventCategoryExpiryDateApplicator;
import com.gumtree.seller.service.advert.expiry.ExpiryDateApplicatorImpl;
import com.gumtree.seller.service.advert.expiry.MotorsExpiryDateApplicator;
import com.gumtree.seller.service.advert.status.AdvertStatusTransitionService;
import com.gumtree.seller.service.advert.expiry.PropertyLoader;
import org.joda.time.DateTime;
import org.joda.time.DateTimeUtils;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Matchers;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;

import static com.gumtree.seller.domain.product.entity.ProductName.BUMP_UP;
import static com.gumtree.seller.domain.product.entity.ProductName.FEATURE_14_DAY;
import static com.gumtree.seller.domain.product.entity.ProductName.FEATURE_3_DAY;
import static com.gumtree.seller.domain.product.entity.ProductName.FEATURE_7_DAY;
import static com.gumtree.seller.domain.product.entity.ProductName.HOMEPAGE_SPOTLIGHT;
import static com.gumtree.seller.domain.product.entity.ProductName.URGENT;
import static com.gumtree.seller.domain.product.entity.ProductName.WEBSITE_URL;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.nullValue;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class ProductApplicatorTest {

    private DateTime now;

    private Clock clock;

    private ProductApplicatorFactory productApplicatorFactory;

    private AdvertListener advertListener;

    private FeatureApplicator featureApplicator;

    private AdvertStatusTransitionService advertStatusTransitionService;

    @Before
    public void setup() {
        now = new DateTime(2010, 1, 1, 12, 30, 0, 0);
        DateTimeUtils.setCurrentMillisFixed(now.toDate().getTime());

        clock = mock(Clock.class);
        when(clock.getDateTime()).thenReturn(now);

        PropertyLoader.loadExpiryDateProperties();
        MotorsExpiryDateApplicator motorsExpiryDateApplicator = new MotorsExpiryDateApplicator();
        motorsExpiryDateApplicator.setClock(clock);

        CarsCategoryExpiryDateApplicator carsCategoryExpiryDateApplicator = new CarsCategoryExpiryDateApplicator();
        carsCategoryExpiryDateApplicator.setClock(clock);

        ExpiryDateApplicatorImpl expiryDateApplicator = new ExpiryDateApplicatorImpl(
                Sets.newHashSet(new EventCategoryExpiryDateApplicator(), motorsExpiryDateApplicator, carsCategoryExpiryDateApplicator));
        expiryDateApplicator.setCategoryModel(TestCategoryModel.loadFromJsonFile("product-applicator-categories.json"));
        expiryDateApplicator.setClock(clock);
        expiryDateApplicator.init();

        advertStatusTransitionService = mock(AdvertStatusTransitionService.class);

        AdvertRepository advertRepository = mock(AdvertRepository.class);
        when(advertRepository.save(Matchers.<Advert>any())).thenAnswer(new Answer<Object>() {
            @Override
            public Object answer(InvocationOnMock invocationOnMock) throws Throwable {
                return invocationOnMock.getArguments()[0];
            }
        });

        advertListener = mock(AdvertListener.class);

        featureApplicator = mock(FeatureApplicator.class);

        productApplicatorFactory = new ProductApplicatorFactoryImpl();

        ReflectionTestUtils.setField(productApplicatorFactory, "clock", clock);
        ReflectionTestUtils.setField(productApplicatorFactory, "advertStatusTransitionService", advertStatusTransitionService);
        ReflectionTestUtils.setField(productApplicatorFactory, "expiryDateApplicator", expiryDateApplicator);
        ReflectionTestUtils.setField(productApplicatorFactory, "featureApplicator", featureApplicator);
        ReflectionTestUtils.setField(productApplicatorFactory, "advertListener", advertListener);
        ReflectionTestUtils.invokeMethod(productApplicatorFactory, "init");
    }

    @After
    public void teardown() {
        Mockito.validateMockitoUsage();
        DateTimeUtils.setCurrentMillisSystem();
    }

    //-------------------------------------------
    // Bump-Up
    //-------------------------------------------

    private ProductApplicator bumpupProductApplicator() {
        ProductApplicator applicator = productApplicatorFactory.createProductApplicator(BUMP_UP);
        ReflectionTestUtils.setField(applicator, "bumpupRestrictionMinutes", 60);
        return applicator;
    }

    @Test(expected = AdvertRemovedException.class)
    public void cannotBumpUpAdvertDeletedByCS() {
        ProductApplicator applicator = bumpupProductApplicator();
        applicator.apply(createAdvert(1L, AdvertStatus.DELETED_CS), null);
    }

    @Test
    public void bumpingUpAUserDeletedAdvertPutsItInAwaitingScreeningStatus() {
        ProductApplicator applicator = bumpupProductApplicator();
        Advert advert = applicator.apply(createAdvert(1L, AdvertStatus.DELETED_USER), null);
        verify(advertStatusTransitionService).apply(AdvertStatus.AWAITING_SCREENING, advert);
    }

    @Test
    public void bumpingUpAnExpiredAdvertPutsItInAwaitingScreeningStatus() {
        ProductApplicator applicator = bumpupProductApplicator();
        Advert advert = applicator.apply(createAdvert(1L, AdvertStatus.EXPIRED), null);
        verify(advertStatusTransitionService).apply(AdvertStatus.AWAITING_SCREENING, advert);
    }

    @Test
    public void bumpingUpAnExpiredAdvertClearsExpiryDate() {
        ProductApplicator applicator = bumpupProductApplicator();
        Advert advert = applicator.apply(createAdvert(1L, AdvertStatus.EXPIRED), null);
        assertThat(advert.getExpiryDate(), is(nullValue()) );
    }

    @Test
    public void bumpingUpALiveAdvertCorrectlyUpdatesPublishedAndExpiryDates() {
        DateTime expiryDate = now.plusDays(30).toDateMidnight().toDateTime();  //gtsell-334
        ProductApplicator applicator = bumpupProductApplicator();
        Advert advert = applicator.apply(createAdvert(1L, AdvertStatus.LIVE), null);
        assertThat(advert.getPublishedDate(), equalTo(now));
        assertThat(advert.getExpiryDate(), equalTo(expiryDate));
    }

    @Test
    public void bumpingUpALiveAdvertUpdatesAdvertBumpupCount() {
        ProductApplicator applicator = bumpupProductApplicator();
        Advert advert = createAdvert(1L, AdvertStatus.LIVE);
        ReflectionTestUtils.setField(advert, "bumpupCount", 45L);
        applicator.apply(advert, null);
        assertThat(advert.getBumpupCount(), equalTo(46L));
    }

    @Test(expected = AdvertRecentlyPublishedException.class)
    public void bumpingUpARecentlyPublishedAdThrowsException() {
        ProductApplicator applicator = bumpupProductApplicator();
        Advert advert = createAdvert(1L, AdvertStatus.LIVE);
        advert.setPublishedDate(now.minusMinutes(20));
        applicator.apply(advert, null);
    }

    @Test
    public void bumpingUpANotSoRecentlyPublishedAdThrowsNoException() {
        ProductApplicator applicator = bumpupProductApplicator();
        Advert advert = createAdvert(1L, AdvertStatus.LIVE);
        advert.setPublishedDate(now.minusMinutes(120));
        applicator.apply(advert, null);
    }

    //-------------------------------------------
    // 3-Day Feature
    //-------------------------------------------
    @Test
    public void anAdvertFeaturedFor3DaysMustBeSetAsFeatured() {
        DateTime expiry = createFeatureExpiry(3);
        ProductApplicator applicator = productApplicatorFactory.createProductApplicator(FEATURE_3_DAY);
        Advert advert = applicator.apply(createAdvert(1L, AdvertStatus.LIVE), null);
        verify(featureApplicator).featureAdvert(advert, FEATURE_3_DAY, expiry, null);
    }

    @Test
    public void featuringALiveAdvertFor3DaysUpdatesExpiryDateIfExpiryLessThan3Days() {
        DateTime lastWeek = now.minusDays(7);
        DateTime tomorrow = now.plusDays(1);
        Advert advert = createAdvert(1L, AdvertStatus.LIVE, tomorrow, lastWeek);
        DateTime expiryDate = createAdvertExpiry(advert, 3);
        ProductApplicator applicator = productApplicatorFactory.createProductApplicator(FEATURE_3_DAY);
        advert = applicator.apply(advert, null);
        verify(featureApplicator).featureAdvert(advert, FEATURE_3_DAY, expiryDate, null);
        assertThat(advert.getExpiryDate(), equalTo(expiryDate));
    }

    @Test
    public void featuringAnAwaitingCSReviewAdvertFor3DaysDoesNotUpdateExpiryDate() {
        Advert advert = createAdvert(1L, AdvertStatus.AWAITING_CS_REVIEW, null, null);
        DateTime expiryDate = createAdvertExpiry(advert, 3);
        ProductApplicator applicator = productApplicatorFactory.createProductApplicator(FEATURE_3_DAY);
        advert = applicator.apply(advert, null);
        verify(featureApplicator).featureAdvert(advert, FEATURE_3_DAY, expiryDate, null);
        assertNull(advert.getExpiryDate());
    }

    @Test(expected = AdvertRemovedException.class)
    public void cannot3DayFeatureAdvertDeletedByCS() {
        productApplicatorFactory
                .createProductApplicator(FEATURE_3_DAY)
                .apply(createAdvert(1L, AdvertStatus.DELETED_CS), null);
    }

    @Test(expected = AdvertRemovedException.class)
    public void cannot3DayFeatureAdvertDeletedByUser() {
        productApplicatorFactory
                .createProductApplicator(FEATURE_3_DAY)
                .apply(createAdvert(1L, AdvertStatus.DELETED_USER), null);
    }

    @Test(expected = AdvertRemovedException.class)
    public void cannot3DayFeatureExpiredAdvert() {
        productApplicatorFactory
                .createProductApplicator(FEATURE_3_DAY)
                .apply(createAdvert(1L, AdvertStatus.EXPIRED), null);
    }

    @Test(expected = AdvertAlreadyFeaturedException.class)
    public void cannot3DayFeatureAdvertThatIsAlready3DayFeatured() {
        Advert advert = createAdvert(1L, AdvertStatus.LIVE);
        addFeatureToAdvert(advert,FEATURE_3_DAY);
        productApplicatorFactory
                .createProductApplicator(FEATURE_3_DAY)
                .apply(advert, null);
    }

    @Test(expected = AdvertAlreadyFeaturedException.class)
    public void cannot3DayFeatureAdvertThatIsAlready7DayFeatured() {
        Advert advert = createAdvert(1L, AdvertStatus.LIVE);
        addFeatureToAdvert(advert,FEATURE_7_DAY);
        productApplicatorFactory
                .createProductApplicator(FEATURE_3_DAY)
                .apply(advert, null);
    }

    @Test(expected = AdvertAlreadyFeaturedException.class)
    public void cannot3DayFeatureAdvertThatIsAlready14DayFeatured() {
        Advert advert = createAdvert(1L, AdvertStatus.LIVE);
        addFeatureToAdvert(advert,FEATURE_14_DAY);
        productApplicatorFactory
                .createProductApplicator(FEATURE_3_DAY)
                .apply(advert, null);
    }

    //-------------------------------------------
    // 7-Day Feature
    //-------------------------------------------
    @Test
    public void anAdvertFeaturedFor7DaysMustBeSetAsFeatured() {
        DateTime expiry = createFeatureExpiry(7);
        ProductApplicator applicator = productApplicatorFactory.createProductApplicator(FEATURE_7_DAY);
        Advert advert = applicator.apply(createAdvert(1L, AdvertStatus.LIVE), null);
        verify(featureApplicator).featureAdvert(advert, FEATURE_7_DAY, expiry, null);
    }

    @Test
    public void featuringALiveAdvertFor7DaysUpdatesExpiryDateIfExpiryLessThan7Days() {
        DateTime lastWeek = now.minusDays(7);
        DateTime tomorrow = now.plusDays(1);
        Advert advert = createAdvert(1L, AdvertStatus.LIVE, tomorrow, lastWeek);
        DateTime expiryDate = createAdvertExpiry(advert, 7);
        ProductApplicator applicator = productApplicatorFactory.createProductApplicator(FEATURE_7_DAY);
        advert = applicator.apply(advert, null);
        verify(featureApplicator).featureAdvert(advert, FEATURE_7_DAY, expiryDate, null);
        assertThat(advert.getExpiryDate(), equalTo(expiryDate));
    }

    @Test
    public void featuringAnAwaitingCSReviewAdvertFor7DaysDoesNotUpdateExpiryDate() {
        Advert advert = createAdvert(1L, AdvertStatus.AWAITING_CS_REVIEW, null, null);
        DateTime expiryDate = createAdvertExpiry(advert, 7);
        ProductApplicator applicator = productApplicatorFactory.createProductApplicator(FEATURE_7_DAY);
        advert = applicator.apply(advert, null);
        verify(featureApplicator).featureAdvert(advert, FEATURE_7_DAY, expiryDate, null);
        assertNull(advert.getExpiryDate());
    }

    @Test(expected = AdvertRemovedException.class)
    public void cannot7DayFeatureAdvertDeletedByCS() {
        productApplicatorFactory
                .createProductApplicator(FEATURE_7_DAY)
                .apply(createAdvert(1L, AdvertStatus.DELETED_CS), null);
    }

    @Test(expected = AdvertRemovedException.class)
    public void cannot7DayFeatureAdvertDeletedByUser() {
        productApplicatorFactory
                .createProductApplicator(FEATURE_7_DAY)
                .apply(createAdvert(1L, AdvertStatus.DELETED_USER), null);
    }

    @Test(expected = AdvertRemovedException.class)
    public void cannot7DayFeatureExpiredAdvert() {
        productApplicatorFactory
                .createProductApplicator(FEATURE_7_DAY)
                .apply(createAdvert(1L, AdvertStatus.EXPIRED), null);
    }

    @Test(expected = AdvertAlreadyFeaturedException.class)
    public void cannot7DayFeatureAdvertThatIsAlready3DayFeatured() {
        Advert advert = createAdvert(1L, AdvertStatus.LIVE);
        addFeatureToAdvert(advert,FEATURE_3_DAY);
        productApplicatorFactory
                .createProductApplicator(FEATURE_7_DAY)
                .apply(advert, null);
    }

    @Test(expected = AdvertAlreadyFeaturedException.class)
    public void cannot7DayFeatureAdvertThatIsAlready7DayFeatured() {
        Advert advert = createAdvert(1L, AdvertStatus.LIVE);
        addFeatureToAdvert(advert,FEATURE_7_DAY);
        productApplicatorFactory
                .createProductApplicator(FEATURE_7_DAY)
                .apply(advert, null);
    }

    @Test(expected = AdvertAlreadyFeaturedException.class)
    public void cannot7DayFeatureAdvertThatIsAlready14DayFeatured() {
        Advert advert = createAdvert(1L, AdvertStatus.LIVE);
        addFeatureToAdvert(advert,FEATURE_14_DAY);
        productApplicatorFactory
                .createProductApplicator(FEATURE_7_DAY)
                .apply(advert, null);
    }

    //-------------------------------------------
    // 14-Day Feature
    //-------------------------------------------
    @Test
    public void anAdvertFeaturedFor14DaysMustBeSetAsFeatured() {
        DateTime expiry = createFeatureExpiry(14);
        ProductApplicator applicator = productApplicatorFactory.createProductApplicator(FEATURE_14_DAY);
        Advert advert = applicator.apply(createAdvert(1L, AdvertStatus.LIVE), null);
        verify(featureApplicator).featureAdvert(advert, FEATURE_14_DAY, expiry, null);
    }

    @Test
    public void featuringALiveAdvertFor14DaysUpdatesExpiryDateIfExpiryLessThan14Days() {
        DateTime lastWeek = now.minusDays(7);
        DateTime tomorrow = now.plusDays(1);
        Advert advert = createAdvert(1L, AdvertStatus.LIVE, tomorrow, lastWeek);
        DateTime expiryDate = createAdvertExpiry(advert, 14);
        ProductApplicator applicator = productApplicatorFactory.createProductApplicator(FEATURE_14_DAY);
        advert = applicator.apply(advert, null);
        verify(featureApplicator).featureAdvert(advert, FEATURE_14_DAY, expiryDate, null);
        assertThat(advert.getExpiryDate(), equalTo(expiryDate));
    }

    @Test
    public void featuringAnAwaitingCSReviewAdvertFor14DaysDoesNotUpdateExpiryDate() {
        Advert advert = createAdvert(1L, AdvertStatus.AWAITING_CS_REVIEW, null, null);
        DateTime expiryDate = createAdvertExpiry(advert, 14);
        ProductApplicator applicator = productApplicatorFactory.createProductApplicator(FEATURE_14_DAY);
        advert = applicator.apply(advert, null);
        verify(featureApplicator).featureAdvert(advert, FEATURE_14_DAY, expiryDate, null);
        assertNull(advert.getExpiryDate());
    }

    @Test(expected = AdvertRemovedException.class)
    public void cannot14DayFeatureAdvertDeletedByCS() {
        productApplicatorFactory
                .createProductApplicator(FEATURE_14_DAY)
                .apply(createAdvert(1L, AdvertStatus.DELETED_CS), null);
    }

    @Test(expected = AdvertRemovedException.class)
    public void cannot14DayFeatureAdvertDeletedByUser() {
        productApplicatorFactory
                .createProductApplicator(FEATURE_14_DAY)
                .apply(createAdvert(1L, AdvertStatus.DELETED_USER), null);
    }

    @Test(expected = AdvertRemovedException.class)
    public void cannot14DayFeatureExpiredAdvert() {
        productApplicatorFactory
                .createProductApplicator(FEATURE_14_DAY)
                .apply(createAdvert(1L, AdvertStatus.EXPIRED), null);
    }

    @Test(expected = AdvertAlreadyFeaturedException.class)
    public void cannot14DayFeatureAdvertThatIsAlready3DayFeatured() {
        Advert advert = createAdvert(1L, AdvertStatus.LIVE);
        addFeatureToAdvert(advert,FEATURE_3_DAY);
        productApplicatorFactory
                .createProductApplicator(FEATURE_14_DAY)
                .apply(advert, null);
    }

    @Test(expected = AdvertAlreadyFeaturedException.class)
    public void cannot14DayFeatureAdvertThatIsAlready7DayFeatured() {
        Advert advert = createAdvert(1L, AdvertStatus.LIVE);
        addFeatureToAdvert(advert,FEATURE_7_DAY);
        productApplicatorFactory
                .createProductApplicator(FEATURE_14_DAY)
                .apply(advert, null);
    }

    @Test(expected = AdvertAlreadyFeaturedException.class)
    public void cannot14DayFeatureAdvertThatIsAlready14DayFeatured() {
        Advert advert = createAdvert(1L, AdvertStatus.LIVE);
        addFeatureToAdvert(advert,FEATURE_14_DAY);
        productApplicatorFactory
                .createProductApplicator(FEATURE_14_DAY)
                .apply(advert, null);
    }

    //-------------------------------------------
    // Urgent Feature
    //-------------------------------------------
    @Test
    public void anAdvertFeaturedAsUrgentMustBeSetAsUrgent() {
        DateTime expiry = createFeatureExpiry(7);
        ProductApplicator applicator = productApplicatorFactory.createProductApplicator(URGENT);
        Advert advert = applicator.apply(createAdvert(1L, AdvertStatus.LIVE), null);
        verify(featureApplicator).featureAdvert(advert, URGENT, expiry, null);
    }

    @Test
    public void featuringALiveAdvertUrgentUpdatesExpiryDateIfExpiryLessThan7Days() {
        DateTime lastWeek = now.minusDays(7);
        DateTime tomorrow = now.plusDays(1);
        Advert advert = createAdvert(1L, AdvertStatus.LIVE, tomorrow, lastWeek);
        DateTime expiryDate = createAdvertExpiry(advert, 7);
        ProductApplicator applicator = productApplicatorFactory.createProductApplicator(URGENT);
        advert = applicator.apply(advert, null);
        verify(featureApplicator).featureAdvert(advert, URGENT, expiryDate, null);
        assertThat(advert.getExpiryDate(), equalTo(expiryDate));
    }

    @Test
    public void featuringAnAwaitingCSReviewAdvertUrgentDoesNotUpdateExpiryDate() {
        Advert advert = createAdvert(1L, AdvertStatus.AWAITING_CS_REVIEW, null, null);
        DateTime expiryDate = createAdvertExpiry(advert, 7);
        ProductApplicator applicator = productApplicatorFactory.createProductApplicator(URGENT);
        advert = applicator.apply(advert, null);
        verify(featureApplicator).featureAdvert(advert, URGENT, expiryDate, null);
        assertNull(advert.getExpiryDate());
    }

    @Test(expected = AdvertRemovedException.class)
    public void cannotFeatureUrgentAdvertDeletedByCS() {
        productApplicatorFactory
                .createProductApplicator(URGENT)
                .apply(createAdvert(1L, AdvertStatus.DELETED_CS), null);
    }

    @Test(expected = AdvertRemovedException.class)
    public void cannotFeatureUrgentAdvertDeletedByUser() {
        productApplicatorFactory
                .createProductApplicator(URGENT)
                .apply(createAdvert(1L, AdvertStatus.DELETED_USER), null);
    }

    @Test(expected = AdvertRemovedException.class)
    public void cannotFeatureUrgentExpiredAdvert() {
        productApplicatorFactory
                .createProductApplicator(URGENT)
                .apply(createAdvert(1L, AdvertStatus.EXPIRED), null);
    }

    //-------------------------------------------
    // Spotlight Feature
    //-------------------------------------------
    @Test(expected = ImageNotFoundException.class)
    public void cannotFeatureSpotlightAdvertWithNoImage() {
        productApplicatorFactory
                .createProductApplicator(HOMEPAGE_SPOTLIGHT)
                .apply(createAdvert(1L, AdvertStatus.LIVE), null);
    }

    @Test(expected = AdvertRemovedException.class)
    public void cannotFeatureSpotlightAdvertDeletedByCS() {
        productApplicatorFactory
                .createProductApplicator(HOMEPAGE_SPOTLIGHT)
                .apply(createAdvert(1L, AdvertStatus.DELETED_CS), null);
    }

    @Test(expected = AdvertRemovedException.class)
    public void cannotFeatureSpotlightAdvertDeletedByUser() {
        productApplicatorFactory
                .createProductApplicator(HOMEPAGE_SPOTLIGHT)
                .apply(createAdvert(1L, AdvertStatus.DELETED_USER), null);
    }

    @Test(expected = AdvertRemovedException.class)
    public void cannotFeatureSpotlightExpiredAdvert() {
        productApplicatorFactory
                .createProductApplicator(HOMEPAGE_SPOTLIGHT)
                .apply(createAdvert(1L, AdvertStatus.EXPIRED), null);
    }

    @Test
    public void anAdvertSpotlightFeaturedMustBeSetAsFeatured() {
        Advert advert = createAdvert(1L, AdvertStatus.LIVE);
        addImageToAdvert(advert);
        DateTime expiry = createFeatureExpiry(7);
        productApplicatorFactory.createProductApplicator(HOMEPAGE_SPOTLIGHT).apply(advert, null);
        verify(featureApplicator).featureAdvert(advert, HOMEPAGE_SPOTLIGHT, expiry, null);
    }

    @Test
    public void anAdvertSpotlightFeatureMustExtendLifeOfAdvertIfExpiring() {
        DateTime lastWeek = now.minusDays(7);
        DateTime tomorrow = now.plusDays(1);
        Advert advert = createAdvert(1L, AdvertStatus.LIVE, tomorrow, lastWeek);
        addImageToAdvert(advert);
        productApplicatorFactory.createProductApplicator(HOMEPAGE_SPOTLIGHT).apply(advert, null);
        DateTime expiryDate = createAdvertExpiry(advert, 7);
        assertThat(advert.getExpiryDate(), equalTo(expiryDate));
    }

    //-------------------------------------------
    // Website URL
    //-------------------------------------------

    @Test(expected = AdvertRemovedException.class)
    public void cannotApplyWebsiteUrlToAdvertDeletedByCS() {
        ProductApplicator applicator = productApplicatorFactory.createProductApplicator(WEBSITE_URL);
        applicator.apply(createAdvert(1L, AdvertStatus.DELETED_CS), null);
    }

    @Test
    public void anAdvertWebsiteUrlShouldNotBeFeaturedTwice() {
        Advert advert = createAdvert(1L, AdvertStatus.LIVE);
        addFeatureToAdvert(advert, WEBSITE_URL);
        DateTime expiry = createFeatureExpiry(36500);
        OrderItem dummyItem = new OrderItem();
        productApplicatorFactory.createProductApplicator(WEBSITE_URL).apply(advert, dummyItem);
        verify(featureApplicator, never()).featureAdvert(eq(advert), eq(ProductName.WEBSITE_URL), eq(expiry), eq(dummyItem));
    }

    @Test
    public void anAdvertWebsiteUrlFeaturedMustBeSetAs100Years() {
        Advert advert = createAdvert(1L, AdvertStatus.LIVE);
        DateTime expiry = createFeatureExpiry(36500);
        productApplicatorFactory.createProductApplicator(WEBSITE_URL).apply(advert, null);
        verify(featureApplicator).featureAdvert(advert, WEBSITE_URL, expiry, null);
    }


    //-------------------------------------------
    // Helper methods
    //-------------------------------------------

    private DateTime createFeatureExpiry(int days) {
        return now.plusDays(days);
    }

    private DateTime createAdvertExpiry(Advert advert, int days) {
        DateTime extended = now.plusDays(days);
        return (extended.isAfter(advert.getExpiryDate())) ? extended : advert.getExpiryDate();
    }

    private Advert createAdvert(Long id, AdvertStatus status) {
        return createAdvert(id, status, now.plusDays(10), now.minusDays(50));
    }

    private Advert createAdvert(Long id, AdvertStatus status, DateTime expiryDate, DateTime publishedDate) {
        Advert advert = new Advert();
        ReflectionTestUtils.setField(advert, "id", id);
        advert.setStatus(status);
        advert.setPublishedDate(publishedDate);
        advert.setExpiryDate(expiryDate);
        advert.setLastModifiedDate(new DateTime());
        advert.setCategory(1L);
        return advert;
    }

    private void addImageToAdvert(Advert advert) {
        Image image = new Image();
        ReflectionTestUtils.setField(image, "id", 1L);
        advert.setImages(Arrays.asList(image), image);
    }

    private void addFeatureToAdvert(Advert advert,ProductName productName) {
        Product product = new Product();
        product.setName(productName);
        AdvertFeature feature = new AdvertFeature();
        feature.setAdvert(advert);
        feature.setProduct(product);
        feature.setExpiryDate(clock.getDateTime().plusDays(1));
        ReflectionTestUtils.setField(advert, "features", Arrays.asList(feature));
    }
}
